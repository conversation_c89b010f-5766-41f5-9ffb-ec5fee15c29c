import * as React from "react";
import Image from "next/image";
import * as <PERSON><PERSON><PERSON><PERSON> from "./hero.module.css";
// import { Carousel } from "react-bootstrap";

// core version + navigation, pagination modules:
import { Autoplay, Navigation, Pagination, EffectFade } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/effect-fade";

const Hero = ({ splash, pageTitle }) => {
  console.log("pageTitle: " + pageTitle);

  return (
    <>
      {!splash ? (
        <>
          {(() => {
            switch (pageTitle) {
              case "reinternational-recruitmentd":
                return (
                  <Image
                    className="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full lg:h-full"
                    src="/images/international.jpg"
                    width={2000}
                    height={687}
                    alt="Mise en Place"
                  />
                );
              case "login":
                return null;
              default:
                return (
                  <Image
                    className="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full lg:h-full"
                    src="/images/about.jpg"
                    width={2000}
                    height={687}
                    alt="Mise en Place"
                  />
                );
            }
          })()}
        </>
      ) : (
        <Swiper
          style={{
            // "--swiper-pagination-color": "#FFBA08",
            // "--swiper-pagination-bullet-inactive-color": "#999999",
            // "--swiper-pagination-bullet-inactive-opacity": "1",
            // "--swiper-pagination-bullet-size": "16px",
            // "--swiper-pagination-bullet-horizontal-gap": "6px",
            "--swiper-theme-color": "#2d343f",
          }}
          navigation
          pagination={{ type: "bullets", clickable: true }}
          autoplay={true}
          speed={4000}
          loop={true}
          effect={"fade"}
          modules={[Autoplay, Navigation, Pagination, EffectFade]}
        >
          <SwiperSlide key={1}>
            <Image
              src="/images/slides/slide-1.png"
              className={HeroStyles.headerImg}
              width={2400}
              height={960}
              alt="Mise en Place"
            />
          </SwiperSlide>
          <SwiperSlide key={2}>
            <Image
              src="/images/slides/slide-2.png"
              className={HeroStyles.headerImg}
              width={2400}
              height={960}
              alt="Mise en Place"
            />
          </SwiperSlide>
          <SwiperSlide key={3}>
            <Image
              src="/images/slides/slide-3.png"
              className={HeroStyles.headerImg}
              width={2400}
              height={960}
              alt="Mise en Place"
            />
          </SwiperSlide>

          <SwiperSlide key={4}>
            <Image
              src="/images/slides/slide-4.png"
              className={HeroStyles.headerImg}
              width={2400}
              height={960}
              alt="Mise en Place"
            />
          </SwiperSlide>
          <SwiperSlide key={5}>
            <Image
              src="/images/slides/slide-5.png"
              className={HeroStyles.headerImg}
              width={2400}
              height={960}
              alt="Mise en Place"
            />
          </SwiperSlide>
          {/* <SwiperSlide key={2}>
            {" "}
            <Image
              src="/images/slides/main-page-2.jpg"
              className={HeroStyles.headerImg}
              width={2400}
              height={960}
              alt="Mise en Place"
            />
          </SwiperSlide>
          <SwiperSlide key={3}>
            {" "}
            <Image
              src="/images/slides/main-page-3.jpg"
              className={HeroStyles.headerImg}
              width={2400}
              height={960}
              alt="Mise en Place"
            />
          </SwiperSlide> */}
        </Swiper>
      )}
    </>
  );
};

export default Hero;
