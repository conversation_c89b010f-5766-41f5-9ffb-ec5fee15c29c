import * as React from "react";
import Link from "next/link";
import * as jobNavStyles from "./jobsNav.module.css";

const JobsNavChefs = ({ link }) => {
  return (
    <>
      <div>
        <div className="flex justify-center w-full">
          <div className="flex flex-wrap gap-0 justify-center">
            <div className="flex space-x-0 pt-2">
              <Link href="executive-chef">
                <div
                  className={
                    link === "executive-chefs"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  EXECUTIVE CHEFS
                </div>
              </Link>
              <Link href="/head-chefs">
                <div
                  className={
                    link === "front"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  HEAD CHEFS
                </div>
              </Link>
              <Link href="/sous-chefs">
                <div
                  className={
                    link === "sous-chefs"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  SOUS CHEFS
                </div>
              </Link>

              <Link href="/chefs-de-partie">
                <div
                  className={
                    link === "chefs-de-partie"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  CHEFS DE PARTIE
                </div>
              </Link>
            </div>
            <div className="flex space-x-0">
              <Link href="/chef-pastry">
                <div
                  className={
                    link === "chef-pastry"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  PASTRY CHEFS /BAKERS
                </div>
              </Link>
              <Link href="/private-chefs">
                <div
                  className={
                    link === "private-chefs"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  PRIVATE CHEFS
                </div>
              </Link>
              <Link href="/chef-manager">
                <div
                  className={
                    link === "chef-manager"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  CHEF MANAGERS
                </div>
              </Link>
              <Link href="/butcher">
                <div
                  className={
                    link === "butcher"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  CHEF MANAGERS
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default JobsNavChefs;
