import * as React from "react";
import PropTypes from "prop-types";
import Link from "next/link";
import * as middleNavStyles from "./middleNav.module.css";
import { Navbar, Nav } from "react-bootstrap";
import { SquareFill } from "react-bootstrap-icons";

const MiddleNav = (splashPage) => {
  const splash = splashPage.splash;

  return (
    <>
      {splash ? (
        <div className="flex flex-row justify-center items-center bg-primary pt-0">
          <div className="grid grid-flow-row lg:grid-flow-col lg:space-x-12 pt-3 pb-3 ">
            <div className="flex flex-row uppercase text-white tracking-wider text-[14px] font-semibold">
              <div className="flex-1 p-1.5">
                <SquareFill color="#ce5f26" />
              </div>
              <div>QUICK CV</div>
            </div>
            <div className="flex flex-row uppercase text-white tracking-wider text-[14px] font-semibold">
              <div className="flex-1 p-1.5">
                <SquareFill color="#ce5f26" />
              </div>
              <div>EMPLOYERS</div>
            </div>
            <div className="flex flex-row uppercase text-white tracking-wider text-[14px] font-semibold">
              <div className="flex-1 p-1.5">
                <SquareFill color="#ce5f26" />
              </div>
              <div>JOB SEARCH</div>
            </div>
            <div className="flex flex-row uppercase text-white tracking-wider text-[14px] font-semibold">
              <div className="flex-1 p-1.5">
                <SquareFill color="#ce5f26" />
              </div>
              <Link href="/testimonials-page">
                <div>TESTIMONIALS</div>
              </Link>
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
};

MiddleNav.propTypes = {
  siteTitle: PropTypes.string,
};

MiddleNav.defaultProps = {
  siteTitle: ``,
};

export default MiddleNav;
