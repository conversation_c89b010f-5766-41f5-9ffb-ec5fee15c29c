import * as React from "react";
import PropTypes from "prop-types";

import Header from "./header";
import Footer from "./footer";

// import Footer from "./footer"
// import "./layout.css";

const Layout = ({ children, splashPage, pageTitle, pageSlug, jobPage }) => {
  return (
    <>
      <Header
        splashPage={splashPage}
        jobPage={jobPage}
        // siteTitle={data.site.siteMetadata?.title || `Title`}
        pageTitle={pageTitle}
      />
      <div
        style={{
          margin: `0 auto`,
          maxWidth: 960,
          padding: `0 1.875rem 1.45rem`,
        }}
      >
        <main>{children}</main>
      </div>
      <Footer />
    </>
  );
};

Layout.propTypes = {
  children: PropTypes.node.isRequired,
};

export default Layout;
