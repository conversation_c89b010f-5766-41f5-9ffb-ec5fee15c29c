import * as React from "react";
import Link from "next/link";
import * as jobNavStyles from "./jobsNav.module.css";

const JobsNav = ({ link }) => {
  return (
    <>
      <div>
        <div className="flex justify-center w-full">
          <div className="flex flex-wrap gap-0 justify-center">
            <div className="flex space-x-0 pt-2">
              <Link href="/senior-chefs">
                <div
                  className={
                    link === "chefs"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  SENIOR CHEFS
                </div>
              </Link>
              <Link href="/front-of-house">
                <div
                  className={
                    link === "front"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  FRONT OF HOUSE MANAGEMENT
                </div>
              </Link>
              <Link href="/sales-marketing-events">
                <div
                  className={
                    link === "sales"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  SALES MARKETING EVENTS
                </div>
              </Link>
            </div>
            <div className="flex space-x-0">
              <Link href="/operations">
                <div
                  className={
                    link === "operations"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  General & operations managers
                </div>
              </Link>
              <Link href="/international">
                <div
                  className={
                    link === "international"
                      ? jobNavStyles.linkTextSelected
                      : jobNavStyles.linkText
                  }
                >
                  INTERNATIONAL
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default JobsNav;
