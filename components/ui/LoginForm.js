import React from "react";
import PropTypes from "prop-types";
import Link from "next/link";

import { useRouter } from "next/router";

const LoginForm = ({ errorMessage, onSubmit }) => (
  <form onSubmit={onSubmit}>
    <div className="bg-white flex flex-col justify-center py-6 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="font-Ubuntu mt-6 text-center text-2xl font-extrabold text-pr">
          Sign into your account
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
        <div className="bg-primary py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-white"
            >
              Email address
            </label>
            <div className="mt-1">
              <input
                // onChange={(event) => setUserEmail(event.target.value)}
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
          </div>

          <div className="mt-4">
            <label
              htmlFor="password"
              className="block text-sm font-medium text-white"
            >
              Password
            </label>
            <div className="mt-1">
              <input
                // onChange={(event) => setUserPass(event.target.value)}
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-orange focus:border-orange sm:text-sm"
              />
            </div>
          </div>

          <div className="mt-6">
            <button
              type="submit"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange"
            >
              Sign in
            </button>

            <div className="mt-0 flex flex-row space-x-2">
              {" "}
              <Link href="/register">
                <button className="mt-3 w-full bg-yellow-600 flex justify-center py-2 px-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange">
                  Register
                </button>
              </Link>
              <Link href="/resetpassword">
                <button className="mt-3 w-full bg-gray-500 flex justify-center py-2 px-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange">
                  Reset password
                </button>
              </Link>
            </div>

            {errorMessage && (
              <p className="mt-3 text-red-600">Incorrect login details</p>
            )}
          </div>
        </div>
      </div>
    </div>
  </form>
);

export default LoginForm;

LoginForm.propTypes = {
  errorMessage: PropTypes.string,
  onSubmit: PropTypes.func,
};
