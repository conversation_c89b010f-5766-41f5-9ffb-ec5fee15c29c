import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";

import useUser from "@lib/useUser";
import fetchJson from "@lib/fetchJson";
import Form from "@components/ui/LoginForm";

const Login = () => {
  const { mutateUser } = useUser({
    redirectTo: "/",
    redirectIfFound: true,
  });

  const [errorMsg, setErrorMsg] = useState("");

  async function handleSubmit(e) {
    e.preventDefault();

    const body = {
      email: e.target.email.value,
      password: e.target.password.value,
    };

    try {
      await mutateUser(
        fetchJson("/api/login", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body),
        })
      );
    } catch (error) {
      setErrorMsg(error);
    }
    // } catch (error) {
    //   console.error("An unexpected error happened:", error);
    //   setErrorMsg(error.message);
    // }
  }

  return (
    <>
      <Form isLogin errorMessage={errorMsg} onSubmit={handleSubmit} />
    </>
  );
};

export default Login;
