import * as React from "react";
import PropTypes from "prop-types";
import Link from "next/link";
import * as topNavStyles from "./topNav.module.css";
import { Navbar, Nav } from "react-bootstrap";
import Image from "next/image";
import Avatar from "react-avatar";
import useUser from "@lib/useUser";
import { useRouter } from "next/router";
import fetchJson from "@lib/fetchJson";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
} from "@headlessui/react";
import { Bars3Icon, BellIcon, XMarkIcon } from "@heroicons/react/24/outline";
import {
  Linkedin,
  Facebook,
  Instagram,
  ChatRightDotsFill,
} from "react-bootstrap-icons";

const navigation = [
  { name: "Home", href: "/", current: true },
  { name: "About", href: "about", current: false },
  { name: "vacancies", href: "latest-vacancies", current: false },
  { name: "employers", href: "employers", current: false },
  { name: "candidates", href: "candidates", current: false },
  { name: "contact", href: "contact", current: false },
  { name: "international", href: "international-recruitment", current: false },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const TopNav = () => {
  const { user, mutateUser } = useUser();
  const router = useRouter();

  const signOutUser = async (e) => {
    e.preventDefault();
    mutateUser(await fetchJson("/api/logout", { method: "POST" }), false);
    router.push("/");
  };

  console.log(user);

  return (
    <Disclosure as="nav" className="bg-primary">
      <div className="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8">
        <div className="relative flex h-16 items-center justify-between">
          <div className="absolute inset-y-0 left-0 flex items-center sm:hidden">
            {/* Mobile menu button*/}
            <DisclosureButton className="group relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
              <span className="absolute -inset-0.5" />
              <span className="sr-only">Open main menu</span>
              <Bars3Icon
                aria-hidden="true"
                className="block size-6 group-data-[open]:hidden"
              />
              <XMarkIcon
                aria-hidden="true"
                className="hidden size-6 group-data-[open]:block"
              />
            </DisclosureButton>
          </div>

          <div className="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
            <div className="flex shrink-0 items-center">
              <Image
                src="/images/logo-white.png"
                width={132}
                height={40}
                alt="Mise en Place"
              />
            </div>
            <div className="hidden sm:ml-14 sm:block">
              <div className="flex space-x-4">
                {navigation.map((item) => (
                  <Link
                    href={item.href}
                    key={item.name}
                    className={classNames(
                      item.current
                        ? " text-white"
                        : "text-white hover:bg-gray-700 rounded-md hover:text-white",
                      "px-2 py-2 text-sm text-white uppercase font-bold tracking-widest"
                    )}
                  >
                    {item.name}
                  </Link>
                ))}
                {user && !user.isLoggedIn ? (
                  <Link
                    href="/login"
                    className="px-2 py-2 text-sm text-yellow-600 uppercase font-bold tracking-widest"
                  >
                    Login
                  </Link>
                ) : null}
              </div>
            </div>
          </div>
          <div className="absolute inset-y-0 right-0  items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0 hidden xl:flex">
            <div className="grid grid-flow-col auto-rows-max gap-3">
              <div>
                <Link
                  href="https://www.facebook.com/miseenplacerecruitment"
                  target="_blank"
                >
                  <Facebook color="#ffffff" />
                </Link>
              </div>

              <div>
                <Link
                  href="https://www.linkedin.com/company/miseenplacerecruitment/"
                  target="_blank"
                >
                  <Linkedin color="#ffffff" />
                </Link>
              </div>

              <div>
                <Link
                  href="https://www.instagram.com/miseenplacerecruitment/"
                  target="_blank"
                >
                  <Instagram color="#ffffff" />
                </Link>
              </div>

              <div>
                <Link href="https://blog.miseenplace.co.uk/" target="_blank">
                  <ChatRightDotsFill color="#ffffff" />
                </Link>
              </div>
            </div>

            {/* Profile dropdown */}
            {user && user.isLoggedIn ? (
              <Menu as="div" className="relative ml-3">
                <div>
                  <MenuButton className="relative flex rounded-full bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                    <span className="absolute -inset-1.5" />
                    <span className="sr-only">Open user menu</span>
                    <Avatar
                      round={true}
                      name={user && user.name}
                      size="36"
                      color="#3E434D"
                      textSizeRatio={3}
                    />
                  </MenuButton>
                </div>
                <MenuItems
                  transition
                  className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                >
                  <MenuItem>
                    <a
                      href="#"
                      className="block px-4 py-2 text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:outline-none"
                    >
                      Profile
                    </a>
                  </MenuItem>
                  {/* <MenuItem>
                    <a
                      href="#"
                      className="block px-4 py-2 text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:outline-none"
                    >
                      Settings
                    </a>
                  </MenuItem> */}
                  <MenuItem>
                    <a
                      href="#"
                      onClick={signOutUser}
                      className="block px-4 py-2 text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:outline-none"
                    >
                      Sign out
                    </a>
                  </MenuItem>
                </MenuItems>
              </Menu>
            ) : null}
          </div>
        </div>
      </div>

      <DisclosurePanel className="sm:hidden">
        <div className="space-y-1 px-2 pb-3 pt-2">
          {navigation.map((item) => (
            <DisclosureButton
              key={item.name}
              as="a"
              href={item.href}
              aria-current={item.current ? "page" : undefined}
              className={classNames(
                item.current
                  ? "bg-gray-900 text-white"
                  : "text-gray-300 hover:bg-gray-700 hover:text-white",
                "block rounded-md px-3 py-2 text-base font-medium"
              )}
            >
              {item.name}
            </DisclosureButton>
          ))}
        </div>
      </DisclosurePanel>
    </Disclosure>
  );
};

TopNav.propTypes = {
  siteTitle: PropTypes.string,
};

TopNav.defaultProps = {
  siteTitle: ``,
};

export default TopNav;
