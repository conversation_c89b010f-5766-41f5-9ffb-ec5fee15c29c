import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import * as bottomNavStyles from "./bottomNav.module.css";
import { Navbar, Nav, Container, Row, Col } from "react-bootstrap";

const BottomNav = () => {
  return (
    <>
      {/* <PagesWrapper /> */}
      <footer className={bottomNavStyles.bgColor}>
        <div className={`${bottomNavStyles.footerNav} container mx-auto px-4`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex justify-center">
              <div className="flex flex-col">
                <Link href="/privacy-policy">
                  <div className={bottomNavStyles.linkText}>
                    (Privacy Policy)
                  </div>
                </Link>
                <Link href="/catering-jobs-in-london">
                  <div className={bottomNavStyles.linkText}>
                    Catering Jobs in London
                  </div>
                </Link>
                <Link href="/catering-jobs-uk">
                  <div className={bottomNavStyles.linkText}>
                    Catering Jobs UK
                  </div>
                </Link>
                <Link href="/catering-jobs-in-london">
                  <div className={bottomNavStyles.linkText}>
                    Catering Management Jobs
                  </div>
                </Link>
                <Link href="/catering-recruitment">
                  <div className={bottomNavStyles.linkText}>
                    Catering Recruitment
                  </div>
                </Link>
                <Link href="/catering-services-international">
                  <div className={bottomNavStyles.linkText}>
                    Catering Services International
                  </div>
                </Link>
                <Link href="/chef-de-partie-jobs">
                  <div className={bottomNavStyles.linkText}>
                    Chef de Partie Jobs
                  </div>
                </Link>
                <Link href="/contract-catering-recruitment">
                  <div className={bottomNavStyles.linkText}>
                    Contract Catering Recruitment
                  </div>
                </Link>
                <Link href="/demi-chef-jobs">
                  <div className={bottomNavStyles.linkText}>Demi Chef Jobs</div>
                </Link>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="flex flex-col">
                <Link href="/executive-chef-jobs">
                  <div className={bottomNavStyles.linkText}>
                    Executive Chef Jobs / Chef de Cuisine
                  </div>
                </Link>
                <Link href="/head-chef-jobs">
                  <div className={bottomNavStyles.linkText}>Head Chef Jobs</div>
                </Link>
                <Link href="/hospitality-jobs">
                  <div className={bottomNavStyles.linkText}>
                    Hospitality Jobs
                  </div>
                </Link>
                <Link href="/hotel-catering-jobs">
                  <div className={bottomNavStyles.linkText}>
                    Hotel Catering Jobs
                  </div>
                </Link>
                <Link href="/international-chef-recruitment">
                  <div className={bottomNavStyles.linkText}>
                    International chef recruitment
                  </div>
                </Link>
                <Link href="/london-restaurant-jobs">
                  <div className={bottomNavStyles.linkText}>
                    London Restaurant Jobs
                  </div>
                </Link>
                <Link href="/private-chef">
                  <div className={bottomNavStyles.linkText}>Private Chef</div>
                </Link>
                <Link href="/restaurant-management-jobs">
                  <div className={bottomNavStyles.linkText}>
                    Restaurant Management Jobs
                  </div>
                </Link>
                <Link href="/sous-chef-jobs">
                  <div className={bottomNavStyles.linkText}>Sous Chef Jobs</div>
                </Link>
                <Link href="/video">
                  <div className={bottomNavStyles.linkText}>Video</div>
                </Link>
              </div>
            </div>
            <div className="flex justify-center items-center">
              <Image
                src="/images/skills.png"
                width={160}
                height={160}
                alt="Mise en Place"
              />
            </div>
          </div>
        </div>
        <Navbar expand="lg" className={bottomNavStyles.bgColor}>
          <div className="mx-auto parent">
            <div className={bottomNavStyles.footerText}>
              Candidates seen by prior appointment only: email{" "}
              <a className="text-orange" href="mailto:<EMAIL>">
                <EMAIL>
              </a>
              <br />
              Address: 17 Hanover Square, London W1S 1BN. Company registered in
              England & Wales No. 3410583 – Tel:{" "}
              <a className="text-orange" href="tel:+44 7 430 9811">
                +44 7 430 9811
              </a>
            </div>
          </div>
        </Navbar>
        <div className="pb-3">
          <div className="mx-auto parent">
            <div className={bottomNavStyles.footerText}>
              © {new Date().getFullYear()} Mise en Place
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default BottomNav;
