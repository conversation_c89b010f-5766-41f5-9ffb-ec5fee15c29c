import * as React from "react";
import Link from "next/link";
import * as JobPostsStyles from "./jobPosts.module.css";
import { Container, Row, Col, Card } from "react-bootstrap";

export default function JobPostsFilter({ posts }) {
  // console.log("posts");
  // console.log(posts);

  if (posts.length > 0) {
    return (
      <Container className={JobPostsStyles.postsContainer}>
        <div className="grid grid-cols-3">
          {posts.map((post, i) => (
            <div key={i} className="p-3">
              {post.attributes.enabled === true &&
              post.attributes.featured === true ? (
                <Link
                  style={{ textDecoration: "none", color: "#ce5f26" }}
                  href={"/" + post.attributes.slug}
                >
                  <Card className="max-w-sm rounded overflow-hidden shadow-lg border border-1">
                    <Card.Header className="bg-orange text-white uppercase text-xs font-bold p-3">
                      {post.attributes.vacancy_type}
                    </Card.Header>
                    <Card.Body className="text-xs text-black p-3 pb-0 font-light tracking-wide leading-4">
                      <Card.Text>{post.attributes.vacancy_subtitle}</Card.Text>
                      <h3 className="font-semibold">Other benefits</h3>
                      <Card.Text>{post.attributes.vacancy_benefits}</Card.Text>
                    </Card.Body>
                    <Card.Header className="text-xs text-orange p-3 font-bold uppercase bg-gray-50">
                      {post.attributes.vacancy_location}
                    </Card.Header>
                    <Card.Header className="text-xs text-bodyt p-3 font-bold uppercase border border-x-0 border-y-1">
                      {post.attributes.vacancy_salary_amount}
                    </Card.Header>
                  </Card>
                </Link>
              ) : null}
            </div>
          ))}
        </div>
      </Container>
    );
  } else {
    return (
      <div className={JobPostsStyles.pageTextNoVac}>
        <p>There are no vacancies in this category currenly.</p>
        <p>
          {" "}
          Register{" "}
          <Link className="text-orange" href="/candidates">
            here
          </Link>{" "}
          and we will let you know when a vacancy in this category becomes
          available
        </p>
      </div>
    );
  }
}

export async function getStaticProps() {
  const res = await fetch(
    process.env.NEXT_PUBLIC_STRAPI_API_URL +
      "/api/job-posts?_sort=id&populate=*"
  );

  // console.log(res);

  const data = await res.json();

  console.log(data);

  const posts = await data.data;

  return {
    props: { posts },
  };
}
