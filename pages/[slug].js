import * as React from "react";
import Layout from "../components/layout";
import JobPosts from "../components/jobPosts";
import useUser from "@lib/useUser";

export default function Page({ page }) {
  const pageData = page.data[0].attributes;

  const { user, mutateUser } = useUser();

  // console.log(user);

  console.log("page");
  console.log(pageData);

  return (
    <Layout splashPage={false} pageTitle={pageData.title} jobPage={false}>
      {/* <Seo title="Home" /> */}
      {/* <PageContent /> */}
      <div>
        <h1
          className={
            "text-lg font-extrabold text-transform: uppercase tracking-widest pb-3 mt-6"
          }
        >
          {pageData.title}
        </h1>
        <div
          className="text-md"
          dangerouslySetInnerHTML={{ __html: pageData.page_content }}
        />
      </div>
    </Layout>

    // <div className="font-semibold text-blue-500">
    //   {/* {pageData.title} */}

    //   <div className="grid grid-flow-col">
    //     <div className="">01</div>
    //     <div className="">02</div>
    //     <div className="">03</div>
    //   </div>
    // </div>
  );
}

export async function getStaticPaths() {
  const res = await fetch(
    process.env.NEXT_PUBLIC_STRAPI_API_URL + "/api/pages"
  );

  const data = await res.json();

  const pages = await data.data;

  //   console.log("pages");
  //   console.log(pages[0].attributes);

  const paths = pages.map((page) => ({
    params: { slug: page.attributes.slug },
  }));

  console.log("paths");
  console.log(paths);

  return {
    paths,
    fallback: false,
  };
}

export async function getStaticProps({ params }) {
  const { slug } = params;

  const res = await fetch(
    process.env.NEXT_PUBLIC_STRAPI_API_URL + `/api/pages?slug=${slug}`
  );

  const page = await res.json();

  return {
    props: { page },
  };
}
