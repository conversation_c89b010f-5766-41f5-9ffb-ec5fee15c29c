import * as React from "react";
import Layout from "../components/layout";
import JobPosts from "../components/jobPosts";
import useUser from "@lib/useUser";
import JobsNav from "../components/jobsNav";
// import Seo from "../components/seo"

export default function Home(posts) {
  const { user, mutateUser } = useUser();

  // console.log(user);

  return (
    <Layout splashPage={false} pageTitle="vacancies" jobPage={true}>
      {/* <Seo title="Home" /> */}

      <JobsNav />
      <JobPosts posts={posts.posts} />
    </Layout>
  );
}

export async function getStaticProps() {
  const res = await fetch(
    process.env.NEXT_PUBLIC_STRAPI_API_URL +
      "/api/job-posts?_sort=id&populate=*"
  );

  // console.log(res);

  const data = await res.json();

  console.log(data);

  const posts = await data.data;

  return {
    props: { posts },
  };
}
