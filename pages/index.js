import * as React from "react";
import Layout from "../components/layout";
import JobPosts from "../components/jobPosts";
import useUser from "@lib/useUser";
// import Seo from "../components/seo"

export default function Home(posts) {
  const { user, mutateUser } = useUser();

  // console.log(user);

  return (
    <Layout splashPage={true} pageTitle="CATERING & HOSPITALITY RECRUITMENT">
      {/* <Seo title="Home" /> */}

      {/* <PageContent /> */}
      <div className="flex flex-row justify-center items-center pb-3 pt-6 text-primary tracking-widest text-md font-extrabold uppercase">
        Latest vacancies
      </div>
      <JobPosts posts={posts.posts} />
    </Layout>
  );
}

export async function getStaticProps() {
  const res = await fetch(
    process.env.NEXT_PUBLIC_STRAPI_API_URL +
      "/api/job-posts?_sort=id&populate=*"
  );

  // console.log(res);

  const data = await res.json();

  console.log(data);

  const posts = await data.data;

  return {
    props: { posts },
  };
}
