import * as React from "react";
import Layout from "../components/layout";
import JobPostsFilter from "../components/jobPostsFilter";
import JobsNav from "../components/jobsNav";
import JobsNavChefs from "../components/jobsNavChefs";

export default function Chefs(posts) {
  return (
    <Layout
      jobPage={true}
      splashPage={false}
      pageTitle="VACANCIES - EXECUTIVE CHEFS"
    >
      <JobsNav link={"chefs"} />
      <JobsNavChefs link={"executive-chefs"} />
      <JobPostsFilter posts={posts.posts} />
    </Layout>
  );
}

export async function getStaticProps() {
  const res = await fetch(
    process.env.NEXT_PUBLIC_STRAPI_API_URL +
      "/api/job-posts?filters[vacancy_type][$eq]=Chefs+-+Executive+Chef&_sort=id&populate=*"
  );

  const data = await res.json();

  const posts = await data.data;

  return {
    props: { posts },
  };
}
