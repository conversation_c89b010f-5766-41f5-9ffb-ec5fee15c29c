import withSession from "../../lib/session";
import sgMail from "@sendgrid/mail";
// import { NextApiRequest, NextApiResponse } from "next";
import axios from "axios";

/// move to utils
// sgMail.setApiKey(
//   "*********************************************************************"
// );

export default withSession(async (req, res) => {
  const { name, email, password } = await req.body;

  try {
    await axios
      .post(
        process.env.NEXT_PUBLIC_STRAPI_API_URL + "/api/auth/local/register",
        {
          username: email,
          email: email,
          password: password,
          name: name,
        }
      )
      .then(() => {
        // console.log("User profile", response.data.user);
        // console.log("User token", response.data.jwt);
        sendEmail(name, email);
        res.json({ message: `Email has been sent` });
      })
      .catch((error) => {
        // Handle error.
        console.log("An error occurred");
        // res.json({ body: error });
        res.status(500).json({ body: session.subscription });
        // console.log("An error occurred:", error);
      });
  } catch (error) {
    // console.error("An unexpected error happened:", error.message);
    res.status(400).json({ error: "Error sending email" });
  }

  async function sendEmail(name, email) {
    const msg = {
      to: email,
      from: "<EMAIL>",
      subject: "Welcome to Authentic Confidence",
      html:
        "Hi " +
        name +
        "," +
        "<p>You are now registered with the Authentic Confidence Hub!</p>" +
        "<p>You should have received an email to verify your email address, please make sure you do that first before logging in.</p>" +
        "<p>As a member you will have blogs, podcasts, special offers and more delivered directly into your inbox.</p>" +
        "<p>To help make our content feel more relevant to you, we want to get to know you a bit better, but let’s do it on your terms! You can tell us a bit about yourself in your <a href='https://authenticconfidence.me/account'>Account page</a></p>" +
        "<p>If you’re ready to take the next step towards Authentic Confidence, then click <a href='https://authenticconfidence.me'>here</a> to purchase your package.</p>" +
        "<p>We look forward to seeing you on the Hub.</p>" +
        "<p>Anna and the team</p>" +
        "<p><a href='mailto:<EMAIL>'><EMAIL></a></p>",
    };

    try {
      await sgMail.send(msg);
      // console.log("message sent to " + email);
      //   res.json({ message: `Email has been sent` });
    } catch (error) {
      //   res.status(500).json({ error: "Error sending email" });
      console.error("error sending message" + error);
    }
  }

  // const CheckUser = async (email) => {
  //   try {
  //     const res = await fetch(
  //       process.env.NEXT_PUBLIC_STRAPI_API_URL +
  //         "/api/users?filters[$and][0][email][$eq]=" +
  //         email,
  //       {
  //         headers: {
  //           Authorization: "Bearer " + user.strapiToken,
  //         },
  //       }
  //     );
  //     return res.json();
  //   } catch (error) {
  //     console.log("Error: " + error);
  //   }
  // };

  // CheckUser(email);

  // lets check user exists first and send back

  // createUser(name, email, password);
  // res.status(200).json({ body: "testing body" });
});
