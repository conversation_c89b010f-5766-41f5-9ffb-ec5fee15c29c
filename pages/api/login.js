import fetchJson from "../../lib/fetchJson";
import withSession from "../../lib/session";
// import Cors from "cors";
// import initMiddleware from "../../lib/init-middleware";
import { createStrapiAxios } from "../../utils/strapi";

export default withSession(async (req, res) => {
  const { email, password } = await req.body;

  // console.log(email, password);

  try {
    const user = await createStrapiAxios()
      .post(`/api/auth/local`, {
        identifier: email,
        password,
      })
      .then((res) => res.data)
      .then((data) => ({
        ...data.user,
        strapiToken: data.jwt,
      }));

    // console.log(user);
    req.session.set("user", user);
    await req.session.save();
    res.json(user);
  } catch (error) {
    console.log("error catch!");
    return res.status(401).json({ error });
  }
});
