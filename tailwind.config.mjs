/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    // extend: {
    //   colors: {
    //     background: "var(--background)",
    //     foreground: "var(--foreground)",
    //   },
    // },
    extend: {
      colors: {
        primary: "#2d343f",
        lightBlue: "#DAEBF1",
        disabled: "#92949c",
        white: "#ffffff",
        black: "#000000",
        orange: "#ce5f26",
        lightorange: "#fc9d03",
      },
      textColor: {
        navyBlue: "#202d56",
        petrolBlue: "#006e96",
        orange: "#e94c28",
        silverGrey: "#a3b2c9",
        zestGreen: "#009b67",
        zestyellow: "#f9bf00",
        zestPink: "#e72277",
        disabled: "#92949c",
        white: "#ffffff",
        black: "#000000",
        bodyt: "#2d343f",
      },
    },
  },
  plugins: [],
};
