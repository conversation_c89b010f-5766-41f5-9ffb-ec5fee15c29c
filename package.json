{"name": "miseenplace-next-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "axios": "^1.8.1", "core-js-pure": "^3.41.0", "cors": "^2.8.5", "next": "15.1.7", "next-iron-session": "^4.2.0", "react": "^19.0.0", "react-avatar": "^5.0.4", "react-bootstrap": "^2.10.9", "react-bootstrap-icons": "^1.11.5", "react-dom": "^19.0.0", "swiper": "^11.2.6", "swr": "^2.3.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1"}}